import { useNavigate } from 'react-router-dom';
import { TableProgramming } from './components/table';
import { createProgrammingColumns } from './components/table/columns';
import { useDeleteProgramming } from './hooks/programming.hook';
import { useFindAllProgramming } from './hooks/find-all-programming.hook';

const ProgrammingPage = () => {
	const { data, isLoading } = useFindAllProgramming();
	const { deleteProgramming } = useDeleteProgramming();
	const navigate = useNavigate();

	const columns = createProgrammingColumns({
		onEdit: (id: string) => navigate(`/programming/${id}/edit`),
		onDelete: (id: string) => void deleteProgramming(id),
		onView: (id: string) => navigate(`/programming/${id}`),
		onDuplicate: null,
	});

	const defaultData = {
		data: [],
		currentPage: 1,
		page: 1,
		pageSize: 10,
		total: 0,
		totalCount: 0,
		totalPages: 1,
	};

	return (
		<TableProgramming
			data={data?.success ? data.data : defaultData}
			columns={columns}
			isLoading={isLoading}
			messageError={data?.success === false ? 'Erro ao carregar programações' : undefined}
		/>
	);
};

export default ProgrammingPage;
