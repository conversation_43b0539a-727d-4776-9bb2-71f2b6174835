import { createSelectColumn } from '@/pages/presentations/components/table/columns/columns/select-column';
import { createDateColumn } from '@/pages/presentations/components/table/columns/columns/date-column';
import { formatDate } from '@/shared/lib/utils/format-date';
import { ColumnDef } from '@tanstack/react-table';

import { Button } from '@/components/shadcnui/button';
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from '@/components/shadcnui/alert-dialog';
import { Edit, Trash2, Settings } from 'lucide-react';
import { IProgramming } from '@/pages/programing/api/requests';

export interface ProgrammingActionsConfig {
	onEdit: ((id: string) => void) | null;
	onDelete?: ((id: string) => void) | null;
	onView?: ((id: string) => void) | null;
	onDuplicate?: ((id: string) => void) | null;
}

type Props = ProgrammingActionsConfig & {
	customColumns?: ColumnDef<IProgramming>[];
};

export const createProgrammingColumns = ({ onEdit, onDelete, onView, onDuplicate, customColumns = [] }: Props): ColumnDef<IProgramming>[] => [
	createSelectColumn<IProgramming>(),
	{
		accessorKey: 'id',
		header: () => <span className="font-medium text-white">ID</span>,
		cell: ({ getValue }) => {
			const value = getValue() as string;
			const shortId = value.slice(0, 8) + '...';
			return <span className="font-mono text-xs text-gray-300">{shortId}</span>;
		},
		enableSorting: true,
		enableHiding: true,
	},
	{
		accessorKey: 'id_devices',
		header: () => <span className="font-medium text-white">Dispositivos</span>,
		cell: ({ getValue }) => {
			const devices = getValue() as string[];
			return (
				<div className="flex flex-col gap-1">
					<span className="text-sm font-medium text-primary">
						{devices.length} dispositivo{devices.length !== 1 ? 's' : ''}
					</span>
					<span className="text-xs text-gray-400">
						{devices.length > 0
							? `${devices
									.slice(0, 2)
									.map((id) => id.slice(0, 8))
									.join(', ')}${devices.length > 2 ? '...' : ''}`
							: 'Nenhum'}
					</span>
				</div>
			);
		},
		enableSorting: false,
		enableHiding: true,
	},
	{
		accessorKey: 'id_presentations',
		header: () => <span className="font-medium text-white">Apresentações</span>,
		cell: ({ getValue }) => {
			const presentations = getValue() as string[];
			return (
				<div className="flex flex-col gap-1">
					<span className="text-sm font-medium text-green-400">
						{presentations.length} apresentaç{presentations.length !== 1 ? 'ões' : 'ão'}
					</span>
					<span className="text-xs text-gray-400">
						{presentations.length > 0
							? `${presentations
									.slice(0, 2)
									.map((id) => id.slice(0, 8))
									.join(', ')}${presentations.length > 2 ? '...' : ''}`
							: 'Nenhuma'}
					</span>
				</div>
			);
		},
		enableSorting: false,
		enableHiding: true,
	},
	createDateColumn<IProgramming>({
		accessorKey: 'createdAt',
		header: 'Criado em',
		formatter: formatDate,
	}),
	createDateColumn<IProgramming>({
		accessorKey: 'updatedAt',
		header: 'Atualizado em',
		formatter: formatDate,
	}),
	{
		id: 'actions',
		header: () => (
			<div className="flex w-full items-center justify-center gap-2">
				<Settings className="h-4 w-4 text-gray-400" />
			</div>
		),
		cell: ({ row }) => {
			const programmingId = row.original.id;
			return (
				<div className="flex items-center justify-end gap-1">
					<Button
						variant="ghost"
						size="icon"
						aria-label="Editar"
						disabled={typeof onEdit !== 'function'}
						onClick={() => onEdit?.(programmingId)}
						className="h-8 w-8 p-2 text-primary hover:text-primary/80"
					>
						<Edit className="h-4 w-4" />
					</Button>
					{onDelete && (
						<AlertDialog>
							<AlertDialogTrigger asChild>
								<Button variant="ghost" size="icon" aria-label="Excluir" className="h-8 w-8 p-2 text-red-500 hover:text-red-600">
									<Trash2 className="h-4 w-4" />
								</Button>
							</AlertDialogTrigger>
							<AlertDialogContent className="border border-border/30 bg-muted/95 backdrop-blur-sm">
								<AlertDialogHeader>
									<div className="flex items-center gap-2">
										<Trash2 className="h-6 w-6 text-red-500" />
										<AlertDialogTitle className="text-lg font-semibold text-foreground">Confirmar exclusão</AlertDialogTitle>
									</div>
									<AlertDialogDescription className="mt-2 text-sm text-muted-foreground">
										Você está prestes a excluir esta programação (ID: {programmingId.slice(0, 8)}...).
										<br />
										Esta ação <span className="font-semibold text-red-500">não pode ser desfeita</span>. Deseja continuar?
									</AlertDialogDescription>
								</AlertDialogHeader>
								<AlertDialogFooter className="gap-2">
									<AlertDialogCancel className="flex items-center gap-1 border border-border/30 bg-background hover:bg-accent">
										<svg width="16" height="16" fill="none" viewBox="0 0 24 24" className="text-muted-foreground">
											<path stroke="currentColor" strokeWidth="2" d="M6 6l12 12M6 18L18 6" />
										</svg>
										Cancelar
									</AlertDialogCancel>
									<AlertDialogAction
										onClick={() => onDelete(programmingId)}
										className="flex items-center gap-1 bg-destructive text-destructive-foreground hover:bg-destructive/90"
									>
										<Trash2 className="h-4 w-4" />
										Excluir
									</AlertDialogAction>
								</AlertDialogFooter>
							</AlertDialogContent>
						</AlertDialog>
					)}
				</div>
			);
		},
		enableSorting: false,
		enableHiding: false,
	},
	...customColumns,
];
