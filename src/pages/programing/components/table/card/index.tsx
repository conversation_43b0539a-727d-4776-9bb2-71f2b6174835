import { Card, CardBody, CardHeader } from '@nextui-org/react';
import { Monitor, Presentation, Calendar } from 'lucide-react';
import { motion } from 'framer-motion';
import { IProgramming } from '../../../api/requests/find-all';
import { formatDate } from '@/shared/lib/utils/format-date';

interface IProgrammingCardProps {
	programming: IProgramming;
}

export const ProgrammingCard = ({ programming }: IProgrammingCardProps) => {
	return (
		<motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>
			<Card
				className="border border-white/10 bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-sm transition-all duration-300 hover:shadow-lg"
				shadow="sm"
			>
				<CardHeader className="pb-2">
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-2">
							<div className="rounded-lg bg-primary/10 p-2">
								<Calendar size={16} className="text-primary" />
							</div>
							<div>
								<p className="text-sm font-semibold">#{programming.id}</p>
								<p className="text-xs text-muted-foreground">Criado em {formatDate(programming.createdAt)}</p>
							</div>
						</div>
					</div>
				</CardHeader>

				<CardBody className="pt-0">
					<div className="space-y-3">
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<Monitor size={14} className="text-blue-500" />
								<span className="text-xs text-muted-foreground">Dispositivos</span>
							</div>
							<span className="text-sm font-medium">{programming.id_devices?.length || 0}</span>
						</div>

						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<Presentation size={14} className="text-green-500" />
								<span className="text-xs text-muted-foreground">Apresentações</span>
							</div>
							<span className="text-sm font-medium">{programming.id_presentations?.length || 0}</span>
						</div>

						{programming.updatedAt && (
							<div className="border-t border-border/40 pt-2">
								<p className="text-xs text-muted-foreground">Atualizado em {formatDate(programming.updatedAt)}</p>
							</div>
						)}
					</div>
				</CardBody>
			</Card>
		</motion.div>
	);
};
