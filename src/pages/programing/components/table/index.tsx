import { Button } from '@/components/shadcnui/button';
import { Table } from '@/components/shadcnui/table';
import { useIsMobile } from '@/hooks/use-is-mobile';
import { CircularProgress, useDisclosure } from '@nextui-org/react';
import { ColumnDef } from '@tanstack/react-table';
import { Calendar } from 'lucide-react';
import { IProgramming, IProgrammingFindAllResponse } from '../../api/requests/find-all';

import { CreateProgrammingModal } from '../create-programming/modal';
import { ProgrammingTableBody } from './body';
import { ProgrammingCard } from './card';
import { ProgrammingTableHeader } from './header';
import { ProgrammingDataTablePagination } from './pagination/container';
import { ProgrammingDataTableToolbar } from './toolbar/toolbar';
import { useProgrammingTable } from '../../hooks/table/programming-table.hook';

export interface IProgrammingTable {
	data: IProgrammingFindAllResponse;
	columns: ColumnDef<IProgramming>[];
	isLoading?: boolean;
	messageError?: string;
}

export const TableProgramming = ({ data, columns, isLoading, messageError }: IProgrammingTable) => {
	const modal = useDisclosure();
	const { table } = useProgrammingTable({ data: data.data, columns });
	const isMobile = useIsMobile(640);

	const renderContent = () => {
		if (messageError) return <div className="py-4 text-center font-medium text-red-500">{messageError}</div>;
		if (isLoading) return <CircularProgress className="mx-auto" aria-label="loading..." />;
		if (isMobile) {
			return (
				<div className="flex flex-col gap-2">
					<div className="mb-2 rounded-lg bg-primary/10 px-3 py-2 text-center text-xs text-primary">
						No mobile, só é possível visualizar as programações existentes.
					</div>
					{data.data.map((programming) => (
						<ProgrammingCard key={programming.id} programming={programming} />
					))}
				</div>
			);
		}
		return (
			<Table>
				<ProgrammingTableHeader table={table} />
				<ProgrammingTableBody table={table} columns={columns} />
			</Table>
		);
	};

	return (
		<section className="relative h-full space-y-4 overflow-hidden rounded-xl px-2 py-2 transition-all duration-100 sm:space-y-6 sm:px-4 sm:py-4">
			<ProgrammingDataTableToolbar table={table} />
			<div className="flex w-full flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
				<div className="space-y-1">
					<p className="text-xs text-muted-foreground sm:text-sm">Gerencie todas as suas programações em um só lugar</p>
				</div>
				<Button
					size="lg"
					className="group mt-2 w-full border border-primary bg-black bg-gradient-to-tr from-primary/20 via-primary/10 to-primary/20 font-bold text-primary shadow-sm transition-all duration-200 hover:bg-primary/10 focus:ring-4 focus:ring-primary/30 sm:mt-0 sm:w-auto"
					onClick={modal.onOpen}
				>
					<Calendar size={22} className="transition-transform duration-300 group-hover:rotate-[15deg] group-hover:scale-110" />
					<span className="ml-2 inline">Nova Programação</span>
				</Button>
			</div>
			<div className={isMobile ? undefined : 'overflow-x-auto rounded-lg border-2 border-white/10'}>
				{renderContent()}
				<ProgrammingDataTablePagination table={table} totalPages={data.totalPages} isMobile={isMobile} />
			</div>

			<CreateProgrammingModal
				size="3xl"
				backdrop="blur"
				isOpen={modal.isOpen}
				onOpenChange={modal.onOpenChange}
				className="border border-input bg-background"
				onClose={modal.onClose}
			/>
		</section>
	);
};
