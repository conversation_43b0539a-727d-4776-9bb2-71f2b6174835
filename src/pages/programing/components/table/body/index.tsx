import { TableBody, TableCell, TableRow } from '@/components/shadcnui/table';
import { ColumnDef, flexRender, Table } from '@tanstack/react-table';
import { IProgramming } from '../../../api/requests/find-all';

interface IProgrammingTableBodyProps {
	table: Table<IProgramming>;
	columns: ColumnDef<IProgramming>[];
}

export const ProgrammingTableBody = ({ table, columns }: IProgrammingTableBodyProps) => {
	return (
		<TableBody>
			{table.getRowModel().rows?.length ? (
				table.getRowModel().rows.map((row) => (
					<TableRow
						key={row.id}
						data-state={row.getIsSelected() && 'selected'}
						className="border-b border-border/40 transition-colors hover:bg-muted/50"
					>
						{row.getVisibleCells().map((cell) => (
							<TableCell key={cell.id} className="px-4 py-3">
								{flexRender(cell.column.columnDef.cell, cell.getContext())}
							</TableCell>
						))}
					</TableRow>
				))
			) : (
				<TableRow>
					<TableCell colSpan={columns.length} className="h-24 text-center">
						<div className="flex flex-col items-center justify-center py-8">
							<p className="text-sm text-muted-foreground">Nenhuma programação encontrada</p>
							<p className="mt-1 text-xs text-muted-foreground">Crie uma nova programação para começar</p>
						</div>
					</TableCell>
				</TableRow>
			)}
		</TableBody>
	);
};
