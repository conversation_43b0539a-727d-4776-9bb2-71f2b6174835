import { Button } from '@/components/shadcnui/button';
import { Input } from '@/components/shadcnui/input';
import { Table } from '@tanstack/react-table';
import { Search, X } from 'lucide-react';

import { ProgrammingDataTableViewOptions } from './view-options';
import { IProgramming } from '@/pages/programing/api/requests';

interface IProgrammingDataTableToolbarProps {
	table: Table<IProgramming>;
}

export const ProgrammingDataTableToolbar = ({ table }: IProgrammingDataTableToolbarProps) => {
	const isFiltered = table.getState().columnFilters.length > 0;

	return (
		<div className="flex items-center justify-between">
			<div className="flex flex-1 items-center space-x-2">
				<div className="relative max-w-sm">
					<Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
					<Input
						placeholder="Buscar programações..."
						value={(table.getColumn('id')?.getFilterValue() as string) ?? ''}
						onChange={(event) => table.getColumn('id')?.setFilterValue(event.target.value)}
						className="h-9 border-input/60 bg-background/60 pl-8 focus:border-primary/60 focus:ring-primary/20"
					/>
				</div>
				{isFiltered && (
					<Button variant="ghost" onClick={() => table.resetColumnFilters()} className="h-8 px-2 lg:px-3">
						Limpar
						<X className="ml-2 h-4 w-4" />
					</Button>
				)}
			</div>
			<ProgrammingDataTableViewOptions table={table} />
		</div>
	);
};
