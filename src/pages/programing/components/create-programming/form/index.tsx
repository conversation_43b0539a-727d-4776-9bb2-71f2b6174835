import { Button } from '@/components/shadcnui/button';
import { Checkbox } from '@/components/shadcnui/checkbox';
import { Input } from '@/components/shadcnui/input';
import { Label } from '@/components/shadcnui/label';
import { ScrollArea } from '@/components/shadcnui/scroll-area';

import { useDevicesAndPresentations } from '@/pages/programing/hooks/use-devices-presentations.hook';
import { CircularProgress } from '@nextui-org/react';
import { Separator } from '@radix-ui/react-dropdown-menu';
import { Monitor, Presentation, Search } from 'lucide-react';
import { useMemo, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';

interface ICreateProgrammingForm {
	id_devices: string[];
	id_presentations: string[];
}

interface ICreateProgrammingFormProps {
	onSubmit: (data: ICreateProgrammingForm) => void;
	isLoading?: boolean;
}

export const CreateProgrammingForm = ({ onSubmit, isLoading = false }: ICreateProgrammingFormProps) => {
	const { devices, presentations, isLoading: isLoadingData } = useDevicesAndPresentations();
	const [deviceSearch, setDeviceSearch] = useState('');
	const [presentationSearch, setPresentationSearch] = useState('');

	const { control, handleSubmit, watch, formState } = useForm<ICreateProgrammingForm>({
		defaultValues: {
			id_devices: [],
			id_presentations: [],
		},
	});

	const selectedDevices = watch('id_devices');
	const selectedPresentations = watch('id_presentations');

	const filteredDevices = useMemo(() => {
		if (!deviceSearch) return devices;
		return devices.filter(
			(device) =>
				device.name.toLowerCase().includes(deviceSearch.toLowerCase()) ||
				device.model.toLowerCase().includes(deviceSearch.toLowerCase()) ||
				device.brand.toLowerCase().includes(deviceSearch.toLowerCase()),
		);
	}, [devices, deviceSearch]);

	const filteredPresentations = useMemo(() => {
		if (!presentationSearch) return presentations;
		return presentations.filter(
			(presentation) =>
				presentation.title.toLowerCase().includes(presentationSearch.toLowerCase()) ||
				presentation.description.toLowerCase().includes(presentationSearch.toLowerCase()),
		);
	}, [presentations, presentationSearch]);

	const handleFormSubmit = (data: ICreateProgrammingForm) => {
		onSubmit(data);
	};

	if (isLoadingData) {
		return (
			<div className="flex items-center justify-center py-8">
				<CircularProgress aria-label="Carregando dados..." />
			</div>
		);
	}

	const isFormValid = selectedDevices.length > 0 && selectedPresentations.length > 0;

	return (
		<form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
			{/* Seleção de Dispositivos */}
			<div className="space-y-4">
				<div className="flex items-center gap-2">
					<Monitor className="h-5 w-5 text-blue-500" />
					<Label className="text-base font-semibold">Dispositivos ({selectedDevices.length} selecionados)</Label>
				</div>

				<div className="relative">
					<Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
					<Input placeholder="Buscar dispositivos..." value={deviceSearch} onChange={(e) => setDeviceSearch(e.target.value)} className="pl-9" />
				</div>

				<ScrollArea className="h-48 rounded-md border p-4">
					{filteredDevices.length === 0 ? (
						<div className="py-8 text-center text-sm text-muted-foreground">
							{deviceSearch ? 'Nenhum dispositivo encontrado' : 'Nenhum dispositivo disponível'}
						</div>
					) : (
						<div className="space-y-2">
							{filteredDevices.map((device) => (
								<Controller
									key={device.id}
									name="id_devices"
									control={control}
									render={({ field }) => (
										<div className="flex items-center space-x-3 rounded-lg p-2 transition-colors hover:bg-muted/50">
											<Checkbox
												id={`device-${device.id}`}
												checked={field.value.includes(device.id.toString())}
												onCheckedChange={(checked) => {
													if (checked) {
														field.onChange([...field.value, device.id.toString()]);
													} else {
														field.onChange(field.value.filter((id) => id !== device.id.toString()));
													}
												}}
											/>
											<div className="min-w-0 flex-1">
												<Label htmlFor={`device-${device.id}`} className="cursor-pointer font-medium">
													{device.name}
												</Label>
												<p className="truncate text-sm text-muted-foreground">
													{device.brand} {device.model} - {device.resolution}
												</p>
											</div>
										</div>
									)}
								/>
							))}
						</div>
					)}
				</ScrollArea>
			</div>

			<Separator />

			{/* Seleção de Apresentações */}
			<div className="space-y-4">
				<div className="flex items-center gap-2">
					<Presentation className="h-5 w-5 text-green-500" />
					<Label className="text-base font-semibold">Apresentações ({selectedPresentations.length} selecionadas)</Label>
				</div>

				<div className="relative">
					<Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
					<Input
						placeholder="Buscar apresentações..."
						value={presentationSearch}
						onChange={(e) => setPresentationSearch(e.target.value)}
						className="pl-9"
					/>
				</div>

				<ScrollArea className="h-48 rounded-md border p-4">
					{filteredPresentations.length === 0 ? (
						<div className="py-8 text-center text-sm text-muted-foreground">
							{presentationSearch ? 'Nenhuma apresentação encontrada' : 'Nenhuma apresentação disponível'}
						</div>
					) : (
						<div className="space-y-2">
							{filteredPresentations.map((presentation) => (
								<Controller
									key={presentation.id}
									name="id_presentations"
									control={control}
									render={({ field }) => (
										<div className="flex items-center space-x-3 rounded-lg p-2 transition-colors hover:bg-muted/50">
											<Checkbox
												id={`presentation-${presentation.id}`}
												checked={field.value.includes(presentation.id)}
												onCheckedChange={(checked) => {
													if (checked) {
														field.onChange([...field.value, presentation.id]);
													} else {
														field.onChange(field.value.filter((id) => id !== presentation.id));
													}
												}}
											/>
											<div className="min-w-0 flex-1">
												<Label htmlFor={`presentation-${presentation.id}`} className="cursor-pointer font-medium">
													{presentation.title}
												</Label>
												<p className="truncate text-sm text-muted-foreground">
													{presentation.description} - {presentation.width}x{presentation.height}
												</p>
											</div>
										</div>
									)}
								/>
							))}
						</div>
					)}
				</ScrollArea>
			</div>

			<div className="flex justify-end pt-4">
				<Button type="submit" disabled={!isFormValid || isLoading} className="w-full sm:w-auto">
					{isLoading ? (
						<>
							<CircularProgress size="sm" className="mr-2" />
							Criando...
						</>
					) : (
						'Criar Programação'
					)}
				</Button>
			</div>
		</form>
	);
};
