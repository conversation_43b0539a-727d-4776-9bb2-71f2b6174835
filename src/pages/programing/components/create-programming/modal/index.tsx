import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dalProps } from '@nextui-org/react';
import { Calendar } from 'lucide-react';

import { CreateProgrammingForm } from '../form';
import { useCreateProgramming } from '@/pages/programing/hooks';

interface ICreateProgrammingModalProps extends Omit<ModalProps, 'children'> {
	onClose?: () => void;
}

export const CreateProgrammingModal = ({ onClose, ...modalConfig }: ICreateProgrammingModalProps) => {
	const { createProgramming, isLoading } = useCreateProgramming(() => onClose?.());

	const handleSubmit = async (data: { id_devices: string[]; id_presentations: string[] }) => {
		await createProgramming({
			data: {
				id_devices: data.id_devices,
				id_presentations: data.id_presentations,
			},
		});
		onClose?.();
	};

	return (
		<Modal {...modalConfig} isDismissable={!isLoading} className="rounded-xl border border-[#232728] bg-muted shadow-lg">
			<ModalContent>
				<ModalHeader className="flex flex-col gap-2 pb-0">
					<div className="flex items-center gap-2">
						<Calendar className="h-6 w-6 text-primary" />
						<h1 className="text-lg font-semibold text-white">Nova Programação</h1>
					</div>
					<h3 className="text-sm font-normal text-muted-foreground">
						Selecione os dispositivos e apresentações para criar uma nova programação
					</h3>
				</ModalHeader>
				<ModalBody className="pb-6 pt-2">
					<CreateProgrammingForm onSubmit={handleSubmit} isLoading={isLoading} />
				</ModalBody>
			</ModalContent>
		</Modal>
	);
};
