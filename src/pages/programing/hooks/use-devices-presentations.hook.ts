import { useQuery } from '@tanstack/react-query';
import { findAllDevicesRequest } from '@/pages/devices/services/request/find-all';
import { findAllPresentations } from '@/pages/presentations/api/requests/presentations/find-all';

export const useDevicesAndPresentations = () => {
	const devicesQuery = useQuery({
		queryKey: ['devices-for-programming'],
		queryFn: () => findAllDevicesRequest({ page: 1, pageSize: 1000 }),
	});

	const presentationsQuery = useQuery({
		queryKey: ['presentations-for-programming'],
		queryFn: () => findAllPresentations({ page: 1, pageSize: 1000 }),
	});

	return {
		devices: devicesQuery.data?.success ? devicesQuery.data.data.data : [],
		presentations: presentationsQuery.data?.success ? presentationsQuery.data.data.data : [],
		isLoadingDevices: devicesQuery.isLoading,
		isLoadingPresentations: presentationsQuery.isLoading,
		isLoading: devicesQuery.isLoading || presentationsQuery.isLoading,
	};
};
